<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
  <meta charset='UTF-8'/>
  <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
  <title>Sports Streaming Site</title>
  <!-- SEO Meta Tags -->
  <meta name="description" content="Your professional sports streaming website."/>
  <meta name="keywords" content="sports, streaming, live, football, soccer, basketball"/>
  <link rel="canonical" href="YOUR_BLOG_URL_HERE" />

  <b:skin>
    <![CDATA[
    /* CSS will go here */
    body {
      font-family: sans-serif;
      margin: 0;
      padding: 0;
      transition: background-color 0.3s, color 0.3s;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 15px;
    }

    /* --- Light Mode --- */
    body.light-mode {
      background-color: #FFFFFF; /* Default light mode background */
      color: #333333;
    }
    body.light-mode .navbar {
      background-color: #0000FF; /* Blue for light mode navbar */
      color: #FFFFFF;
    }
    body.light-mode .navbar a {
      color: #FFFFFF;
    }
    body.light-mode .footer {
      background-color: #f1f1f1;
      color: #333333;
    }
    body.light-mode .news-ticker {
      background-color: #FF0000; /* Red background for news ticker */
      color: white;
    }
    body.light-mode .news-ticker .important {
        color: yellow; /* Example, adjust as needed */
    }


    /* --- Dark Mode --- */
    body.dark-mode {
      background-color: #121212; /* Dark background */
      color: #e0e0e0;
    }
    body.dark-mode .navbar {
      background-color: #000080; /* Navy for dark mode navbar */
      color: #e0e0e0;
    }
    body.dark-mode .navbar a {
      color: #e0e0e0;
    }
    body.dark-mode .footer {
      background-color: #1f1f1f;
      color: #e0e0e0;
    }
    body.dark-mode .news-ticker {
      background-color: #FF0000; /* Red background for news ticker */
      color: white;
    }
    body.dark-mode .news-ticker .important {
        color: yellow; /* Example, adjust as needed */
    }


    .navbar {
      padding: 1rem 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .navbar .logo {
      font-size: 1.5rem;
      font-weight: bold;
    }

    .nav-links {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
    }

    .nav-links li {
      margin-left: 20px;
    }

    .nav-links a {
      text-decoration: none;
      font-weight: bold;
    }
    .nav-links a:hover, .nav-links a.active {
      text-decoration: underline;
    }

    .controls {
      display: flex;
      align-items: center;
    }

    .lang-switcher, .theme-switcher {
      margin-left: 15px;
      padding: 5px 10px;
      cursor: pointer;
      border: 1px solid; /* Set border color in light/dark themes */
      border-radius: 5px;
    }
    body.light-mode .lang-switcher, body.light-mode .theme-switcher {
        border-color: #0000FF;
        background-color: #FFFFFF;
        color: #0000FF;
    }
    body.dark-mode .lang-switcher, body.dark-mode .theme-switcher {
        border-color: #e0e0e0;
        background-color: #000080;
        color: #e0e0e0;
    }


    .news-ticker {
      padding: 10px;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      box-sizing: border-box;
    }
    .news-ticker .important {
        font-weight: bold;
        animation: blinker 1s linear infinite;
    }
    .news-ticker-content {
        display: inline-block;
        padding-left: 100%;
        animation: ticker-scroll 30s linear infinite;
    }

    @keyframes blinker {
        50% { opacity: 0; }
    }
    @keyframes ticker-scroll {
        0% { transform: translateX(0); }
        100% { transform: translateX(-100%); }
    }

    /* Match Calendar Navigation */
    .match-calendar-nav button {
      padding: 8px 15px;
      margin: 0 5px;
      border: 1px solid #ccc;
      background-color: #f0f0f0;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.3s, color 0.3s;
    }
    .match-calendar-nav button:hover {
      background-color: #e0e0e0;
    }
    .match-calendar-nav button.active-cal-btn {
      font-weight: bold;
      border-width: 2px;
    }
    #cal-yesterday.active-cal-btn { background-color: #d0e0f0; border-color: #a0c0e0;} /* Light blueish */
    #cal-today.active-cal-btn { background-color: #d0f0d0; border-color: #a0e0a0;} /* Light greenish */
    #cal-tomorrow.active-cal-btn { background-color: #f0f0d0; border-color: #e0e0a0;} /* Light yellowish */

    body.dark-mode .match-calendar-nav button {
      background-color: #333;
      border-color: #555;
      color: #e0e0e0;
    }
    body.dark-mode .match-calendar-nav button:hover {
      background-color: #444;
    }
    body.dark-mode #cal-yesterday.active-cal-btn { background-color: #2a3f54; border-color: #4a6f8a;}
    body.dark-mode #cal-today.active-cal-btn { background-color: #2a543f; border-color: #4a8a6f;}
    body.dark-mode #cal-tomorrow.active-cal-btn { background-color: #54542a; border-color: #8a8a4a;}


    .main-content {
      padding: 20px 0;
    }

    .content-section {
      display: none; /* Hidden by default */
      padding: 20px;
      border: 1px solid #ccc; /* Placeholder border */
      margin-top: 10px;
      border-radius: 5px;
    }
    .content-section.active {
      display: block;
    }
    body.light-mode .content-section {
        border-color: #0000FF;
        background-color: #f9f9f9;
    }
    body.dark-mode .content-section {
        border-color: #000080;
        background-color: #2a2a2a;
    }


    .footer {
      text-align: center;
      padding: 20px 0;
      margin-top: 30px;
    }
    .footer-links-container {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        padding: 10px 0;
    }
    .footer-column {
        margin: 10px;
    }
    .footer-column h4 {
        margin-bottom: 10px;
    }
    .footer-column ul {
        list-style: none;
        padding: 0;
    }
    .footer-column ul li a {
        text-decoration: none;
    }
    body.light-mode .footer-column ul li a {
        color: #0000FF;
    }
    body.dark-mode .footer-column ul li a {
        color: #ADD8E6; /* Light blue for dark mode links */
    }


    /* Ad placeholders */
    .ad-placeholder {
        background-color: #f0f0f0;
        border: 1px dashed #ccc;
        text-align: center;
        padding: 20px;
        margin: 10px 0;
        min-height: 90px; /* Example height */
        display: flex;
        align-items: center;
        justify-content: center;
    }
    body.dark-mode .ad-placeholder {
        background-color: #333;
        border-color: #555;
        color: #aaa;
    }

    /* Video Player Container Styling */
    .video-player-container {
      margin-bottom: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    body.dark-mode .video-player-container {
      border-color: #444;
    }
    .video-player-container h4 {
      margin-top: 0;
    }


    /* Responsive Design */
    @media (max-width: 768px) {
      .nav-links {
        flex-direction: column;
        align-items: center;
        width: 100%;
      }
      .nav-links li {
        margin: 10px 0;
      }
      .navbar {
        flex-direction: column;
      }
      .controls {
        margin-top: 10px;
      }
      .footer-links-container {
        flex-direction: column;
        align-items: center;
      }
    }
    ]]>
  </b:skin>
<link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
<link rel="stylesheet" href="https://cdn.fluidplayer.com/v3/current/fluidplayer.min.css" />
</head>
<body>
  <!-- Blogger Sections for Layout Customization -->

  <b:section class='sidebar-section' id='sidebar_section' name='Sidebar Ads' showaddelement='yes' tag='aside'>
      <b:widget id='HTML2' locked='false' title='Sidebar Ad 1' type='HTML' version='2' visible='true'>
          <b:includable id='main'>
              <div class='ad-placeholder'>Ad Placeholder 12 (Sidebar)</div>
          </b:includable>
      </b:widget>
      <b:widget id='HTML3' locked='false' title='Sidebar Ad 2' type='HTML' version='2' visible='true'>
          <b:includable id='main'>
              <div class='ad-placeholder'>Ad Placeholder 13 (Sidebar)</div>
          </b:includable>
      </b:widget>
  </b:section>

  <b:section class='footer-section' id='footer_section' maxwidgets='1' name='Footer Section' showaddelement='yes'>
    <b:widget id='HTML14' locked='false' title='Footer Content' type='HTML' version='2' visible='true'>
      <b:widget-settings>
        <b:widget-setting name='content'>© H-A-2025. All Rights Reserved.</b:widget-setting>
      </b:widget-settings>
      <b:includable id='main'>
        <div class='footer-content-widget'>
          <div class="footer-links-container">
            <div class="footer-column">
                <h4 data-i18n="european_leagues">Ligues Européennes</h4>
                <ul>
                    <li><a href="#" data-i18n="french_league">Ligue française</a></li>
                    <li><a href="#" data-i18n="english_league">Ligue anglaise</a></li>
                    <li><a href="#" data-i18n="italian_league">Ligue italienne</a></li>
                    <li><a href="#" data-i18n="german_league">Ligue allemande</a></li>
                    <li><a href="#">England FA Cup</a></li>
                    <li><a href="#">European Championship</a></li>
                    <li><a href="#">UEFA Champions League</a></li>
                    <li><a href="#">Germany Bundesliga</a></li>
                    <li><a href="#">Italy Serie A</a></li>
                    <li><a href="#">Spain Primera Division</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h4 data-i18n="arab_leagues">Ligues Arabes</h4>
                <ul>
                    <li><a href="#" data-i18n="egyptian_league">Ligue égyptienne</a></li>
                    <li><a href="#" data-i18n="saudi_league">Ligue saoudienne</a></li>
                    <li><a href="#" data-i18n="algerian_league">Ligue algérienne</a></li>
                    {/* Add more as needed */}
                </ul>
            </div>
            <div class="footer-column">
                <h4 data-i18n="international_competitions">International</h4>
                <ul>
                    <li><a href="#">FIFA World Cup</a></li>
                    <li><a href="#">Coupe d'Europe</a></li>
                    <li><a href="#">Coupe d'Afrique</a></li>
                    <li><a href="#">Coupe américaine</a></li>
                    <li><a href="#">Coupe d'Asie</a></li>
                </ul>
            </div>
             <div class="footer-column">
                <h4 data-i18n="social_media">Réseaux Sociaux</h4>
                <ul id="social-media-links-footer">
                    {/* Links will be added by JS or widget settings */}
                    <li><a href="#" data-i18n="facebook">Facebook</a></li>
                    <li><a href="#" data-i18n="twitter">Twitter</a></li>
                    <li><a href="#" data-i18n="instagram">Instagram</a></li>
                </ul>
            </div>
          </div>
          <p><data:content/></p>
          <div class='ad-placeholder'>Ad Placeholder 14 (Footer)</div>
        </div>
      </b:includable>
    </b:widget>
  </b:section>

  <div class='container'>
    <header class='navbar'>
      <!-- Logo will be rendered by b:widget -->
      <b:section class='navbar-section' id='navbar_section' maxwidgets='1' name='Navigation Bar Section' showaddelement='yes'>
        <b:widget id='HTML1' locked='false' title='Header Content (Logo/Site Title)' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Your Logo/Site Title Here</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='logo'>
              <data:content/>
            </div>
          </b:includable>
        </b:widget>
      </b:section>

      <nav>
        <ul class='nav-links'>
          <li><a href='#accueil' class='nav-item active' data-section='accueil' data-i18n='home'>Accueil</a></li>
          <li><a href='#actualites' class='nav-item' data-section='actualites' data-i18n='news'>Actualités</a></li>
          <li><a href='#statistiques' class='nav-item' data-section='statistiques' data-i18n='statistics'>Statistiques</a></li>
          <li><a href='#match-calendar' class='nav-item' data-section='match-calendar' data-i18n='match_calendar_nav'>Calendrier</a></li>
          <li><a href='#videos' class='nav-item' data-section='videos' data-i18n='videos'>Vidéos</a></li>
          <li><a href='#live' class='nav-item' data-section='live' data-i18n='live_nav'>Live</a></li>
          <li><a href='#chaines' class='nav-item' data-section='chaines' data-i18n='channels'>Chaînes</a></li>
          <li><a href='#contact' class='nav-item' data-section='contact' data-i18n='contact_nav'>Contact</a></li>
          <li><a href='#blog-archive' class='nav-item' data-section='blog-archive' data-i18n='blog_archive_nav'>Archive</a></li>
        </ul>
      </nav>
      <div class='controls'>
        <select id='lang-switcher-select'>
          <option value='fr'>FR</option>
          <option value='en'>EN</option>
          <option value='ar'>AR</option>
        </select>
        <button id='theme-switcher-button' data-i18n="toggle_theme_btn">Mode Sombre/Clair</button>
      </div>
    </header>

    <b:section class='news-ticker-section' id='news_ticker_section' maxwidgets='1' name='News Ticker Section' showaddelement='yes'>
      <b:widget id='HTML4' locked='false' title='Animated News Ticker' type='HTML' version='2' visible='true'>
        <b:widget-settings>
          <b:widget-setting name='content'>Default news item 1; Default news item 2; Default news item 3</b:widget-setting>
        </b:widget-settings>
        <b:includable id='main'>
          <div class='news-ticker-content-widget'>
            <!-- Content will be populated by JavaScript from widget settings or API -->
            <!-- <span class='important' data-i18n='important'>Important</span>: <span id='news-ticker-text-widget'><data:content/></span> -->
          </div>
        </b:includable>
      </b:widget>
    </b:section>

    <main class='main-content'>
      <!-- Content sections will be rendered by b:widgets -->
      <b:section class='main-content-section' id='main_content_section' name='Main Content Area' showaddelement='yes'>
        <b:widget id='HTML5' locked='false' title='Accueil Content' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Welcome to the Home Page!</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='accueil'>
              <h2 data-i18n="home_title">Accueil</h2>
              <p><data:content/></p>
              <div class='ad-placeholder'>Ad Placeholder 1 (Accueil)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML6' locked='false' title='Actualités Content' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Latest news will appear here.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='actualites'>
              <h2 data-i18n="news_title">Actualités</h2>
              <div id="news-content-api">Loading news...</div>
              <div class='ad-placeholder'>Ad Placeholder 2 (Actualités)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML7' locked='false' title='Statistiques Content' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Stats will be displayed here.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='statistiques'>
              <h2 data-i18n="stats_title">Statistiques</h2>
              <p><data:content/></p>
              <!-- League Stats Dropdown -->
              <div>
                <label for="league-select-stats" data-i18n="select_league_stats">Select League (Stats):</label>
                <select id="league-select-stats">
                  <option value="" data-i18n="select_option">-- Select --</option>
                  {/* Options will be populated by JS */}
                </select>
                <div id="league-standings-content"></div>
              </div>
              <br/>
              <!-- Scorer Stats Dropdown -->
              <div>
                <label for="league-select-scorers" data-i18n="select_league_scorers">Select League (Top Scorers):</label>
                <select id="league-select-scorers">
                  <option value="" data-i18n="select_option">-- Select --</option>
                  {/* Options will be populated by JS */}
                </select>
                <div id="top-scorers-content"></div>
              </div>
              <div class='ad-placeholder'>Ad Placeholder 3 (Statistiques)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML8' locked='false' title='Match Calendar' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Match calendar will appear here.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='match-calendar'>
              <h2 data-i18n="match_calendar_title">Calendrier des Matchs</h2>
              <div class="match-calendar-nav">
                <button id="cal-yesterday" data-i18n="yesterday">Hier</button>
                <button id="cal-today" data-i18n="today">Aujourd'hui</button>
                <button id="cal-tomorrow" data-i18n="tomorrow">Demain</button>
              </div>
              <div id="match-list-container">
                {/* Matches will be loaded here by JavaScript */}
              </div>
              <div class='ad-placeholder'>Ad Placeholder 4 (Match Calendar)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML9' locked='false' title='Vidéos Content' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Videos will be embedded here.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='videos'>
              <h2 data-i18n="videos_title">Vidéos</h2>

              <h3 data-i18n="youtube_videos_title">YouTube Videos</h3>
              <div id="videos-content-youtube">Loading videos...</div>

              <h3 data-i18n="direct_videos_title">Direct MP4/M3U8 Videos (Plyr)</h3>
              <div id="videos-content-direct">
                <!-- Example MP4 Video with Plyr -->
                <div class="video-player-container">
                  <h4 data-i18n="example_mp4_title">Example MP4 Video</h4>
                  <video class="js-player static-plyr-player" playsinline="true" controls="true" data-poster="https://via.placeholder.com/640x360.png?text=MP4+Video+Poster">
                    <source src="https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/360/Big_Buck_Bunny_360_10s_1MB.mp4" type="video/mp4" />
                    <!-- Fallback for browsers that don't support the video tag -->
                    <a href="https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/360/Big_Buck_Bunny_360_10s_1MB.mp4" download="Big_Buck_Bunny_360_10s_1MB.mp4">Download MP4</a>
                  </video>
                </div>

                <!-- Example M3U8 Video with Plyr (Requires HLS.js for browsers that don't support HLS natively) -->
                <div class="video-player-container">
                  <h4 data-i18n="example_m3u8_title">Example M3U8 Stream (HLS)</h4>
                  <video class="js-player static-plyr-player" playsinline="true" controls="true" data-poster="https://via.placeholder.com/640x360.png?text=M3U8+Stream+Poster">
                    <source src="https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8" type="application/x-mpegURL" />
                     <a href="https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8" download="x36xhzz.m3u8">Download M3U8</a>
                  </video>
                </div>

                <!-- Example Fluid Player Video -->
                <div class="video-player-container">
                  <h4 data-i18n="example_fluid_title">Example Fluid Player Video</h4>
                  <video id="fluid-player-example" class="static-fluid-player" style="width: 100%; height: auto;">
                    <source src="http://distribution.bbb3d.renderfarming.net/video/mp4/bbb_sunflower_1080p_30fps_normal.mp4" type="video/mp4" title="Big Buck Bunny MP4" />
                  </video>
                </div>

                <!-- Example Clappr Video -->
                <div class="video-player-container">
                  <h4 data-i18n="example_clappr_title">Example Clappr Video (M3U8)</h4>
                  <div id="clappr-player-example" class="static-clappr-player" style="width: 100%; height: 360px;"></div>
                </div>

                <!-- Example JW Player Video -->
                <div class="video-player-container">
                    <h4 data-i18n="example_jwplayer_title">Example JW Player Video (MP4)</h4>
                    <div id="jwplayer-example" class="static-jwplayer">Loading JW Player...</div>
                </div>
              </div>

              <div class='ad-placeholder'>Ad Placeholder 5 (Vidéos)</div>
              <div class='ad-placeholder'>Ad Placeholder 6 (Vidéos)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML10' locked='false' title='Live Content' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Live streams will appear here.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='live'>
              <h2 data-i18n="live_title">Live</h2>
              <div id="live-content-youtube">Loading live streams...</div>
              <div class='ad-placeholder'>Ad Placeholder 7 (Live)</div>
              <div class='ad-placeholder'>Ad Placeholder 8 (Live)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML11' locked='false' title='Chaînes TV Content' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <!-- Default channels: Name|Link|i18nKey -->
            <b:widget-setting name='content'>Bein Sport|#|bein_sport;SSC|#|ssc_sport;Sky Sport|#|sky_sport;DAZN|#|dazn_sport</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='chaines'>
              <h2 data-i18n="channels_title">Chaînes TV</h2>
              <ul id="tv-channels-list">
                {/* Channels will be populated by JavaScript from widget settings or TV_CHANNELS_CONFIG */}
              </ul>
              <div class='ad-placeholder'>Ad Placeholder 9 (Chaînes)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML12' locked='false' title='Contact Page' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Contact us using the form below. Email: <EMAIL></b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='contact'>
              <h2 data-i18n="contact_title">Contactez-nous</h2>
              <p><data:content/></p>
              <form id="contact-form">
                <label for="contact-name" data-i18n="contact_name_label">Nom:</label><br/>
                <input type="text" id="contact-name" name="name" required="true"/><br/>
                <label for="contact-email" data-i18n="contact_email_label">Email:</label><br/>
                <input type="email" id="contact-email" name="email" required="true"/><br/>
                <label for="contact-message" data-i18n="contact_message_label">Message:</label><br/>
                <textarea id="contact-message" name="message" required="true"></textarea><br/>
                <button type="submit" data-i18n="contact_send_button">Envoyer</button>
              </form>
              <div id="contact-form-status"></div>
              <div class='ad-placeholder'>Ad Placeholder 10 (Contact)</div>
            </div>
          </b:includable>
        </b:widget>
        <b:widget id='HTML13' locked='false' title='Blog Archive' type='HTML' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='content'>Recent posts will be listed here.</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='content-section-widget' data-section-id='blog-archive'>
              <h2 data-i18n="blog_archive_title">Archives du Blog</h2>
              <div id="blog-archive-list">
                {/* Blog archive links will be populated by Blogger or JS */}
                <p><data:content/></p>
              </div>
              <div class='ad-placeholder'>Ad Placeholder 11 (Archive)</div>
            </div>
          </b:includable>
        </b:widget>
      </b:section>
    </main>

    <b:section id='sidebar_section'/>

    <footer class='footer'>
      <b:section id='footer_section'/>
    </footer>
  </div>

  <script type='text/javascript'>
  // <![CDATA[
    // --- Global Variables & API Keys ---
    const API_KEYS = {
        FOOTBALL_DATA: '0df257bd1b0040fd87ad064265838f98',
        NEWSDATA_IO: 'pub_8016419b5a444d9338fc88ed3b631185e5f39',
        YOUTUBE: 'AIzaSyCbG1vso1OuwHMBkEeN8uQtYVOdUaDXzXs' // Be cautious with API keys in client-side code
    };

    const NEWS_TICKER_ITEMS_CONFIG = "Actualité importante 1;Breaking News: Équipe X gagne le match!;Prochain match: Équipe A vs Équipe B demain à 20h00"; // Default, can be overridden by widget

    const TV_CHANNELS_CONFIG = [
        { name: "Bein Sport", link: "#", i18n_key: "bein_sport" },
        { name: "SSC", link: "#", i18n_key: "ssc_sport" },
        { name: "Sky Sport", link: "#", i18n_key: "sky_sport" },
        { name: "DAZN", link: "#", i18n_key: "dazn_sport" }
    ];

    const SOCIAL_MEDIA_LINKS_CONFIG = [
        { name: "Facebook", url: "https://facebook.com/yourpage", iconClass: "fab fa-facebook", i18n_key: "facebook" },
        { name: "Twitter", url: "https://twitter.com/yourprofile", iconClass: "fab fa-twitter", i18n_key: "twitter" },
        { name: "Instagram", url: "https://instagram.com/yourprofile", iconClass: "fab fa-instagram", i18n_key: "instagram" },
        { name: "YouTube", url: "https://youtube.com/yourchannel", iconClass: "fab fa-youtube", i18n_key: "youtube" }
    ];


    // --- Translations ---
    const translations = {
      en: {
        home: "Home", news: "News", statistics: "Statistics", videos: "Videos", live_nav: "Live", channels: "Channels",
        important: "Important", home_title: "Home", news_title: "News", stats_title: "Statistics",
        videos_title: "Videos", live_title: "Live", channels_title: "Channels",
        toggle_theme_btn: "Toggle Theme",
        yesterday: "Yesterday", today: "Today", tomorrow: "Tomorrow",
        match_calendar_nav: "Calendar", match_calendar_title: "Match Calendar",
        not_started: "Not Started Yet", soon: "Soon", in_progress: "In Progress", match_finished: "Match Finished",
        select_league_stats: "Select League (Standings):", select_league_scorers: "Select League (Top Scorers):",
        select_option: "-- Select --",
        contact_nav: "Contact", contact_title: "Contact Us", blog_archive_nav: "Archive", blog_archive_title: "Blog Archive",
        contact_name_label: "Name:", contact_email_label: "Email:", contact_message_label: "Message:", contact_send_button: "Send",
        european_leagues: "European Leagues", french_league: "French League", english_league: "English League",
        italian_league: "Italian League", german_league: "German League",
        arab_leagues: "Arab Leagues", egyptian_league: "Egyptian League", saudi_league: "Saudi League", algerian_league: "Algerian League",
        international_competitions: "International", social_media: "Social Media",
        bein_sport: "Bein Sport", ssc_sport: "SSC", sky_sport: "Sky Sport", dazn_sport: "DAZN Sport",
        facebook: "Facebook", twitter: "Twitter", instagram: "Instagram", youtube: "YouTube",
        youtube_videos_title: "YouTube Videos", direct_videos_title: "Direct MP4/M3U8 Videos (Plyr)",
        example_mp4_title: "Example MP4 Video", example_m3u8_title: "Example M3U8 Stream (HLS)",
        select_league_placeholder_stats: "Select a league to view standings.",
        select_league_placeholder_scorers: "Select a league to view top scorers.",
        example_fluid_title: "Example Fluid Player Video",
        example_clappr_title: "Example Clappr Video",
        example_jwplayer_title: "Example JW Player Video"
      },
      fr: {
        home: "Accueil", news: "Actualités", statistics: "Statistiques", videos: "Vidéos", live_nav: "Live", channels: "Chaînes",
        important: "Important", home_title: "Accueil", news_title: "Actualités", stats_title: "Statistiques",
        videos_title: "Vidéos", live_title: "Live", channels_title: "Chaînes TV",
        toggle_theme_btn: "Changer Thème",
        yesterday: "Hier", today: "Aujourd'hui", tomorrow: "Demain",
        match_calendar_nav: "Calendrier", match_calendar_title: "Calendrier des Matchs",
        not_started: "Pas encore commencé", soon: "Bientôt", in_progress: "En cours", match_finished: "Fin du match",
        select_league_stats: "Sélectionner Ligue (Classement):", select_league_scorers: "Sélectionner Ligue (Buteurs):",
        select_option: "-- Sélectionner --",
        contact_nav: "Contact", contact_title: "Contactez-nous", blog_archive_nav: "Archive", blog_archive_title: "Archives du Blog",
        contact_name_label: "Nom:", contact_email_label: "Email:", contact_message_label: "Message:", contact_send_button: "Envoyer",
        european_leagues: "Ligues Européennes", french_league: "Ligue française", english_league: "Ligue anglaise",
        italian_league: "Ligue italienne", german_league: "Ligue allemande",
        arab_leagues: "Ligues Arabes", egyptian_league: "Ligue égyptienne", saudi_league: "Ligue saoudienne", algerian_league: "Ligue algérienne",
        international_competitions: "International", social_media: "Réseaux Sociaux",
        bein_sport: "Bein Sport", ssc_sport: "SSC", sky_sport: "Sky Sport", dazn_sport: "DAZN Sport",
        facebook: "Facebook", twitter: "Twitter", instagram: "Instagram", youtube: "YouTube",
        youtube_videos_title: "Vidéos YouTube", direct_videos_title: "Vidéos Directes MP4/M3U8 (Plyr)",
        example_mp4_title: "Exemple Vidéo MP4", example_m3u8_title: "Exemple Flux M3U8 (HLS)",
        select_league_placeholder_stats: "Sélectionnez une ligue pour voir le classement.",
        select_league_placeholder_scorers: "Sélectionnez une ligue pour voir les meilleurs buteurs.",
        example_fluid_title: "Exemple Vidéo Fluid Player",
        example_clappr_title: "Exemple Vidéo Clappr",
        example_jwplayer_title: "Exemple Vidéo JW Player"
      },
      ar: {
        home: "الرئيسية", news: "الأخبار", statistics: "الإحصائيات", videos: "الفيديوهات", live_nav: "مباشر", channels: "القنوات",
        important: "هام", home_title: "الرئيسية", news_title: "الأخبار", stats_title: "الإحصائيات",
        videos_title: "الفيديوهات", live_title: "بث مباشر", channels_title: "القنوات التلفزيونية",
        toggle_theme_btn: "تغيير السمة",
        yesterday: "أمس", today: "اليوم", tomorrow: "غداً",
        match_calendar_nav: "الجدول", match_calendar_title: "جدول المباريات",
        not_started: "لم تبدأ بعد", soon: "قريباً", in_progress: "جارية الآن", match_finished: "انتهت المباراة",
        select_league_stats: "اختر الدوري (الترتيب):", select_league_scorers: "اختر الدوري (الهدافون):",
        select_option: "-- اختر --",
        contact_nav: "اتصل بنا", contact_title: "اتصل بنا", blog_archive_nav: "الأرشيف", blog_archive_title: "أرشيف المدونة",
        contact_name_label: "الاسم:", contact_email_label: "البريد الإلكتروني:", contact_message_label: "الرسالة:", contact_send_button: "إرسال",
        european_leagues: "الدوريات الأوروبية", french_league: "الدوري الفرنسي", english_league: "الدوري الإنجليزي",
        italian_league: "الدوري الإيطالي", german_league: "الدوري الألماني",
        arab_leagues: "الدوريات العربية", egyptian_league: "الدوري المصري", saudi_league: "الدوري السعودي", algerian_league: "الدوري الجزائري",
        international_competitions: "دولية", social_media: "وسائل التواصل",
        bein_sport: "بي إن سبورت", ssc_sport: "إس إس سي", sky_sport: "سكاي سبورت", dazn_sport: "دازن سبورت",
        facebook: "فيسبوك", twitter: "تويتر", instagram: "انستجرام", youtube: "يوتيوب",
        youtube_videos_title: "فيديوهات يوتيوب", direct_videos_title: "فيديوهات مباشرة MP4/M3U8 (Plyr)",
        example_mp4_title: "مثال فيديو MP4", example_m3u8_title: "مثال بث M3U8 (HLS)",
        select_league_placeholder_stats: "اختر دوريًا لعرض الترتيب.",
        select_league_placeholder_scorers: "اختر دوريًا لعرض الهدافين.",
        example_fluid_title: "مثال فيديو Fluid Player",
        example_clappr_title: "مثال فيديو Clappr",
        example_jwplayer_title: "مثال فيديو JW Player"
      }
    };
    let currentLang = 'fr'; // Default language

    // --- DOM Ready ---
    document.addEventListener('DOMContentLoaded', function() {
      const navItems = document.querySelectorAll('.nav-item');
      const contentSections = document.querySelectorAll('.content-section-widget'); // Target widget divs
      const themeSwitcher = document.getElementById('theme-switcher-button');
      const langSwitcherSelect = document.getElementById('lang-switcher-select');
      const body = document.body;

      // --- Initial Setup ---
      function initializeSite() {
        // Set initial theme (e.g., from localStorage or default to light)
        const savedTheme = localStorage.getItem('theme') || 'light-mode';
        body.classList.add(savedTheme);
        if (savedTheme === 'dark-mode') {
            themeSwitcher.textContent = getTranslation('toggle_theme_btn_light') || "Mode Clair";
        } else {
            themeSwitcher.textContent = getTranslation('toggle_theme_btn_dark') || "Mode Sombre";
        }


        // Set initial language
        const savedLang = localStorage.getItem('language') || 'fr';
        currentLang = savedLang;
        langSwitcherSelect.value = currentLang;
        updateAllTexts(currentLang);

        // Show default section (Accueil)
        showSection('accueil');
        setActiveLink('accueil');

        // Initialize News Ticker
        initNewsTicker();

        // Initialize TV Channels List
        initTvChannels();

        // Initialize Social Media Links
        initSocialMediaLinks();

        // Initialize Match Calendar Buttons
        initMatchCalendarControls();

        // Load initial data for sections (can be expanded)
        loadNews();
        loadMatchCalendar('today'); // Load today's matches by default
        loadLeagueOptionsForDropdowns(); // For stats section
        initializeStaticPlyrPlayers(); // Initialize static Plyr players
        initializeStaticFluidPlayers(); // Initialize static Fluid players
        
        // Set initial placeholder text for stats sections
        const standingsContainer = document.getElementById('league-standings-content');
        if (standingsContainer) {
            standingsContainer.innerHTML = `<p>${getTranslation('select_league_placeholder_stats')}</p>`;
        }
        const scorersContainer = document.getElementById('top-scorers-content');
        if (scorersContainer) {
            scorersContainer.innerHTML = `<p>${getTranslation('select_league_placeholder_scorers')}</p>`;
        }

        // loadVideos(); // YouTube API calls are now part of searchYouTube
        // loadLiveStreams(); // YouTube API calls are now part of searchYouTube
      }


      // --- Navigation ---
      navItems.forEach(item => {
        item.addEventListener('click', function(event) {
          event.preventDefault();
          const sectionId = this.getAttribute('data-section');
          showSection(sectionId);
          setActiveLink(sectionId);
          // Smooth scroll to section (optional)
          // document.getElementById(sectionId + '-section-content').scrollIntoView({ behavior: 'smooth' });
        });
      });

      function showSection(sectionId) {
        contentSections.forEach(section => {
          if (section.getAttribute('data-section-id') === sectionId) {
            section.style.display = 'block'; // Or use a class like 'active'
            section.classList.add('active');
          } else {
            section.style.display = 'none';
            section.classList.remove('active');
          }
        });
      }

      function setActiveLink(sectionId) {
          navItems.forEach(link => {
              link.classList.remove('active');
              if (link.getAttribute('data-section') === sectionId) {
                  link.classList.add('active');
              }
          });
      }

      // --- Theme Switcher ---
      themeSwitcher.addEventListener('click', function() {
        body.classList.toggle('dark-mode');
        body.classList.toggle('light-mode');
        const isDarkMode = body.classList.contains('dark-mode');
        localStorage.setItem('theme', isDarkMode ? 'dark-mode' : 'light-mode');
        this.textContent = isDarkMode ? (getTranslation('toggle_theme_btn_light') || "Mode Clair") : (getTranslation('toggle_theme_btn_dark') || "Mode Sombre");
        updateAllTexts(currentLang); // Re-apply translations as button text changes
      });


      // --- Language Switcher ---
      langSwitcherSelect.addEventListener('change', function() {
        currentLang = this.value;
        localStorage.setItem('language', currentLang);
        updateAllTexts(currentLang);
      });

      function getTranslation(key) {
          return translations[currentLang][key] || translations['en'][key] || key; // Fallback to English then key itself
      }

      function updateAllTexts(lang) {
        document.querySelectorAll('[data-i18n]').forEach(el => {
          const key = el.getAttribute('data-i18n');
          // Handle specific cases like button text based on theme
          if (key === 'toggle_theme_btn') {
            const isDarkMode = body.classList.contains('dark-mode');
            el.textContent = isDarkMode ? (translations[lang]['toggle_theme_btn_light'] || "Mode Clair") : (translations[lang]['toggle_theme_btn_dark'] || "Mode Sombre");
          } else {
            el.textContent = translations[lang][key] || translations['en'][key] || el.getAttribute('data-i18n'); // Fallback
          }
        });
        // Update dynamic content if necessary, e.g., news ticker 'Important'
        const importantSpan = document.querySelector('.news-ticker .important');
        if(importantSpan) importantSpan.textContent = getTranslation('important');

        // Update TV Channel names if they are dynamically generated with i18n keys
        initTvChannels(); // Re-initialize to update names
        initSocialMediaLinks(); // Re-initialize to update names
        if (typeof loadLeagueOptionsForDropdowns === "function") { // Ensure function exists
            loadLeagueOptionsForDropdowns(); // Re-populate and translate league dropdowns
        }
        // Also update placeholder texts if they are visible
        const standingsContainer = document.getElementById('league-standings-content');
        if (standingsContainer && (!standingsContainer.hasChildNodes() || standingsContainer.textContent.trim() === '')) {
            standingsContainer.innerHTML = `<p>${getTranslation('select_league_placeholder_stats')}</p>`;
        }
        const scorersContainer = document.getElementById('top-scorers-content');
        if (scorersContainer && (!scorersContainer.hasChildNodes() || scorersContainer.textContent.trim() === '')) {
            scorersContainer.innerHTML = `<p>${getTranslation('select_league_placeholder_scorers')}</p>`;
        }
      }
      // Add more specific translations for theme button
      translations.en.toggle_theme_btn_dark = "Dark Mode";
      translations.en.toggle_theme_btn_light = "Light Mode";
      translations.fr.toggle_theme_btn_dark = "Mode Sombre";
      translations.fr.toggle_theme_btn_light = "Mode Clair";
      translations.ar.toggle_theme_btn_dark = "الوضع الداكن";
      translations.ar.toggle_theme_btn_light = "الوضع الفاتح";


      // --- News Ticker ---
      function initNewsTicker() {
        const tickerWidget = document.getElementById('HTML4');
        const tickerContentElement = document.querySelector('#news_ticker_section .news-ticker-content-widget');
        if (!tickerContentElement) return;

        let newsItemsString = NEWS_TICKER_ITEMS_CONFIG; // Default
        if (tickerWidget) {
            const widgetSettings = tickerWidget.querySelector('b\\:widget-settings');
            if (widgetSettings) {
                const setting = widgetSettings.querySelector('b\\:widget-setting[name="content"]');
                if (setting && setting.textContent) {
                    newsItemsString = setting.textContent;
                }
            }
        }
        
        const newsItems = newsItemsString.split(';').map(item => item.trim()).filter(item => item);
        if (newsItems.length > 0) {
            tickerContentElement.innerHTML = `<span class="important" data-i18n="important">${getTranslation('important')}</span>: <span class="news-items-scroll">${newsItems.join(' &nbsp; | &nbsp; ')}</span>`;
            // Re-apply animation if needed, or ensure CSS handles it
            const scrollSpan = tickerContentElement.querySelector('.news-items-scroll');
            if(scrollSpan) {
                // Force reflow to restart animation if items change
                scrollSpan.style.animation = 'none';
                scrollSpan.offsetHeight; /* trigger reflow */
                scrollSpan.style.animation = null; 
                scrollSpan.style.animationName = 'ticker-scroll';
                scrollSpan.style.animationDuration = (newsItems.join(' &nbsp; | &nbsp; ').length / 10) + 's'; // Adjust speed based on length
            }
        } else {
            tickerContentElement.innerHTML = `<span class="important" data-i18n="important">${getTranslation('important')}</span>: <span data-i18n="no_news">No news available.</span>`;
        }
        updateAllTexts(currentLang); // Ensure "Important" is translated
      }


      // --- TV Channels List ---
      function initTvChannels() {
          const listElement = document.getElementById('tv-channels-list');
          if (!listElement) return;
          listElement.innerHTML = ''; // Clear existing
          TV_CHANNELS_CONFIG.forEach(channel => {
              const listItem = document.createElement('li');
              const link = document.createElement('a');
              link.href = channel.link || '#';
              link.textContent = getTranslation(channel.i18n_key) || channel.name;
              link.target = "_blank"; // Open in new tab if it's an external link
              listItem.appendChild(link);
              listElement.appendChild(listItem);
          });
      }

      // --- Social Media Links ---
      function initSocialMediaLinks() {
          const container = document.getElementById('social-media-links-footer');
          if (!container) return;
          container.innerHTML = ''; // Clear existing
          SOCIAL_MEDIA_LINKS_CONFIG.forEach(social => {
              const listItem = document.createElement('li');
              const link = document.createElement('a');
              link.href = social.url;
              link.target = "_blank";
              link.setAttribute('aria-label', getTranslation(social.i18n_key) || social.name);
              // If you use Font Awesome or similar for icons:
              // const icon = document.createElement('i');
              // icon.className = social.iconClass; // e.g., "fab fa-facebook"
              // link.appendChild(icon);
              // link.innerHTML += ` ${getTranslation(social.i18n_key) || social.name}`; // Text next to icon
              link.textContent = getTranslation(social.i18n_key) || social.name; // Just text for now
              listItem.appendChild(link);
              container.appendChild(listItem);
          });
      }

      // --- Image Lazy Loading ---
      function lazyLoadImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        const observer = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              observer.unobserve(img);
            }
          });
        });
        lazyImages.forEach(img => observer.observe(img));
      }
      lazyLoadImages(); // Call it

      // --- API Fetch Helper ---
      async function fetchData(url, options = {}) {
          try {
              const response = await fetch(url, options);
              if (!response.ok) {
                  console.error(`HTTP error! status: ${response.status} for URL: ${url}`);
                  const errorData = await response.text(); // Try to get more error info
                  console.error("Error data:", errorData);
                  throw new Error(`Network response was not ok: ${response.statusText}`);
              }
              return await response.json();
          } catch (error) {
              console.error('Fetch error:', error);
              return null; // Or throw error to be caught by caller
          }
      }

      // --- News API (NewsData.io) ---
      async function loadNews() {
          const newsContainer = document.getElementById('news-content-api');
          if (!newsContainer) return;
          newsContainer.innerHTML = getTranslation('loading_news') || 'Loading news...';

          // Prioritize q=sports, football, soccer. Language based on currentLang.
          let langParam = 'en'; // Default for API
          if (currentLang === 'fr') langParam = 'fr';
          else if (currentLang === 'ar') langParam = 'ar';
          
          const apiUrl = `https://newsdata.io/api/1/news?apikey=${API_KEYS.NEWSDATA_IO}&q=sport%20OR%20football%20OR%20soccer&language=${langParam}&country=fr,gb,us,es,it,de,sa,eg,dz`; // Example countries

          const data = await fetchData(apiUrl);
          if (data && data.results && data.results.length > 0) {
              newsContainer.innerHTML = '';
              data.results.slice(0, 10).forEach(article => { // Display top 10 articles
                  const articleDiv = document.createElement('div');
                  articleDiv.className = 'news-article';
                  articleDiv.innerHTML = `
                      <h3><a href="${article.link}" target="_blank">${article.title}</a></h3>
                      ${article.image_url ? `<img src="${article.image_url}" alt="${article.title}" style="max-width:100px; height:auto; float:left; margin-right:10px;" loading="lazy">` : ''}
                      <p>${article.description || article.snippet || ''}</p>
                      <small>${new Date(article.pubDate).toLocaleDateString()}</small><hr/>
                  `;
                  // Lazy load image if data-src is used instead of src directly
                  // const img = articleDiv.querySelector('img');
                  // if (img && article.image_url) img.setAttribute('data-src', article.image_url);

                  newsContainer.appendChild(articleDiv);
              });
              lazyLoadImages(); // Re-check for new lazy images
          } else {
              newsContainer.innerHTML = getTranslation('no_news_found') || 'No news articles found.';
          }
      }
      translations.en.loading_news = "Loading news...";
      translations.fr.loading_news = "Chargement des actualités...";
      translations.ar.loading_news = "جاري تحميل الأخبار...";
      translations.en.no_news_found = "No news articles found.";
      translations.fr.no_news_found = "Aucun article d'actualité trouvé.";
      translations.ar.no_news_found = "لم يتم العثور على مقالات إخبارية.";


      // --- Match Calendar (football-data.org) ---
      const MATCH_STATUS_STYLES = {
          SCHEDULED: { textKey: 'not_started', color: 'gray' },
          TIMED: { textKey: 'soon', color: 'orange' },
          IN_PLAY: { textKey: 'in_progress', color: 'red', blinking: true },
          PAUSED: { textKey: 'in_progress', color: 'red', blinking: true }, // Or a different status like "Half-time"
          FINISHED: { textKey: 'match_finished', color: 'green' },
          SUSPENDED: { textKey: 'match_suspended', color: 'purple' }, // Add translation
          POSTPONED: { textKey: 'match_postponed', color: 'blue' }, // Add translation
          CANCELLED: { textKey: 'match_cancelled', color: 'black' }, // Add translation
      };
      translations.en.match_suspended = "Suspended"; translations.fr.match_suspended = "Suspendu"; translations.ar.match_suspended = "موقوف";
      translations.en.match_postponed = "Postponed"; translations.fr.match_postponed = "Reporté"; translations.ar.match_postponed = "مؤجل";
      translations.en.match_cancelled = "Cancelled"; translations.fr.match_cancelled = "Annulé"; translations.ar.match_cancelled = "ملغى";


      function initMatchCalendarControls() {
          const yesterdayBtn = document.getElementById('cal-yesterday');
          const todayBtn = document.getElementById('cal-today');
          const tomorrowBtn = document.getElementById('cal-tomorrow');

          if(yesterdayBtn) yesterdayBtn.addEventListener('click', () => loadMatchCalendar('yesterday'));
          if(todayBtn) todayBtn.addEventListener('click', () => loadMatchCalendar('today'));
          if(tomorrowBtn) tomorrowBtn.addEventListener('click', () => loadMatchCalendar('tomorrow'));

          // Style the active button
          const buttons = [yesterdayBtn, todayBtn, tomorrowBtn];
          buttons.forEach(btn => {
              if(btn) btn.addEventListener('click', (e) => {
                  buttons.forEach(b => b && b.classList.remove('active-cal-btn'));
                  e.target.classList.add('active-cal-btn');
              });
          });
          if(todayBtn) todayBtn.classList.add('active-cal-btn'); // Default to today
      }

      async function loadMatchCalendar(period) { // period: 'yesterday', 'today', 'tomorrow'
          const matchListContainer = document.getElementById('match-list-container');
          if (!matchListContainer) return;
          matchListContainer.innerHTML = getTranslation('loading_matches') || 'Loading matches...';

          let dateFrom, dateTo;
          const today = new Date();
          if (period === 'today') {
              dateFrom = today.toISOString().split('T')[0];
              dateTo = dateFrom;
          } else if (period === 'yesterday') {
              const yesterday = new Date(today);
              yesterday.setDate(today.getDate() - 1);
              dateFrom = yesterday.toISOString().split('T')[0];
              dateTo = dateFrom;
          } else if (period === 'tomorrow') {
              const tomorrow = new Date(today);
              tomorrow.setDate(today.getDate() + 1);
              dateFrom = tomorrow.toISOString().split('T')[0];
              dateTo = dateFrom;
          }

          // Fetch matches for a range of popular competitions. You might want to make this configurable.
          // Free tier of football-data.org is limited. Consider specific competitions.
          // Example: Premier League (PL), Champions League (CL), La Liga (PD), Serie A (SA), Bundesliga (BL1)
          // For free tier, often only a few competitions are available. Let's try a general approach.
          const apiUrl = `https://api.football-data.org/v4/matches?dateFrom=${dateFrom}&dateTo=${dateTo}`;
          
          const data = await fetchData(apiUrl, { headers: { 'X-Auth-Token': API_KEYS.FOOTBALL_DATA } });

          if (data && data.matches && data.matches.length > 0) {
              matchListContainer.innerHTML = '';
              data.matches.forEach(match => {
                  const matchDiv = document.createElement('div');
                  matchDiv.className = 'match-item';
                  // Add a class for the day to allow specific styling if needed later
                  matchDiv.classList.add(`match-day-${period}`);
                  
                  const statusInfo = MATCH_STATUS_STYLES[match.status] || { textKey: match.status, color: 'grey' };
                  const statusText = getTranslation(statusInfo.textKey) || match.status;
                  
                  let score = '-';
                  if (match.score && match.score.fullTime) {
                      if (match.score.fullTime.home !== null && match.score.fullTime.away !== null) {
                         score = `${match.score.fullTime.home} - ${match.score.fullTime.away}`;
                      }
                  }
                  if (match.status === "IN_PLAY" && match.score && match.score.halfTime) { // Or current score if available
                     if (match.score.fullTime.home !== null && match.score.fullTime.away !== null) { // API v4 uses fullTime for current score during IN_PLAY
                         score = `${match.score.fullTime.home} - ${match.score.fullTime.away}`;
                     } else if (match.score.halfTime.home !== null && match.score.halfTime.away !== null) {
                         score = `${match.score.halfTime.home} - ${match.score.halfTime.away} (HT)`;
                     }
                  }


                  matchDiv.innerHTML = `
                      <div class="match-time">${new Date(match.utcDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                      <div class="match-teams">
                          <span class="team-home">${match.homeTeam.name}</span>
                          <span class="match-score">${score}</span>
                          <span class="team-away">${match.awayTeam.name}</span>
                      </div>
                      <div class="match-status" style="color:${statusInfo.color}; ${statusInfo.blinking ? 'animation: blinker 1s linear infinite;' : ''}">
                          ${statusText}
                      </div>
                      <div class="match-competition">${match.competition.name}</div>
                  `;
                  matchListContainer.appendChild(matchDiv);
              });
          } else if (data && data.matches && data.matches.length === 0) {
              matchListContainer.innerHTML = getTranslation('no_matches_period') || `No matches found for ${period}.`;
          } else {
              matchListContainer.innerHTML = getTranslation('error_loading_matches') || 'Error loading matches. The API might be rate-limited or the selected period has no data for available competitions.';
          }
      }
      translations.en.loading_matches = "Loading matches...";
      translations.fr.loading_matches = "Chargement des matchs...";
      translations.ar.loading_matches = "جاري تحميل المباريات...";
      translations.en.no_matches_period = "No matches found for this period.";
      translations.fr.no_matches_period = "Aucun match trouvé pour cette période.";
      translations.ar.no_matches_period = "لم يتم العثور على مباريات لهذه الفترة.";
      translations.en.error_loading_matches = "Error loading matches.";
      translations.fr.error_loading_matches = "Erreur lors du chargement des matchs.";
      translations.ar.error_loading_matches = "خطأ في تحميل المباريات.";


      // --- Statistics Section (football-data.org) ---
      const LEAGUE_IDS = { // Common league codes for football-data.org
          // Top European Leagues
          "Premier League (EN)": "PL", // England
          "Ligue 1 (FR)": "FL1",      // France
          "Bundesliga (DE)": "BL1",   // Germany
          "Serie A (IT)": "SA",       // Italy
          "La Liga (ES)": "PD",       // Spain
          "UEFA Champions League": "CL",
          // Arab Leagues - These might not be in free tier or require specific IDs
          "Egyptian Premier League": "EG1", // Placeholder, check API for actual code
          "Saudi Professional League": "SA1", // Placeholder
          "Algerian Ligue Professionnelle 1": "DZ1", // Placeholder
          // International
          "FIFA World Cup": "WC"
      };
      const leagueDisplayNames = {
          en: { PL: "Premier League", FL1: "Ligue 1", BL1: "Bundesliga", SA: "Serie A", PD: "La Liga", CL: "Champions League", WC: "FIFA World Cup", EG1: "Egyptian Premier League", SA1: "Saudi Pro League", DZ1: "Algerian Ligue 1"},
          fr: { PL: "Premier League", FL1: "Ligue 1", BL1: "Bundesliga", SA: "Serie A", PD: "La Liga", CL: "Ligue des Champions", WC: "Coupe du Monde FIFA", EG1: "Première Ligue Égyptienne", SA1: "Ligue Pro Saoudienne", DZ1: "Ligue 1 Algérienne"},
          ar: { PL: "الدوري الإنجليزي الممتاز", FL1: "الدوري الفرنسي الدرجة الأولى", BL1: "الدوري الألماني", SA: "الدوري الإيطالي", PD: "الدوري الإسباني", CL: "دوري أبطال أوروبا", WC: "كأس العالم لكرة القدم", EG1: "الدوري المصري الممتاز", SA1: "دوري المحترفين السعودي", DZ1: "الرابطة الجزائرية المحترفة الأولى"}
      };

      function getLeagueDisplayName(code) {
          return leagueDisplayNames[currentLang][code] || code;
      }

      async function loadLeagueOptionsForDropdowns() {
          const leagueSelectStats = document.getElementById('league-select-stats');
          const leagueSelectScorers = document.getElementById('league-select-scorers');

          if (leagueSelectStats) {
              leagueSelectStats.innerHTML = `<option value="">${getTranslation('select_option')}</option>`; // Clear and add default
              for (const name in LEAGUE_IDS) {
                  const option = document.createElement('option');
                  option.value = LEAGUE_IDS[name];
                  option.textContent = getLeagueDisplayName(LEAGUE_IDS[name]); // Use translated name
                  leagueSelectStats.appendChild(option);
              }
              leagueSelectStats.addEventListener('change', (e) => loadLeagueStandings(e.target.value));
          }

          if (leagueSelectScorers) {
              leagueSelectScorers.innerHTML = `<option value="">${getTranslation('select_option')}</option>`; // Clear and add default
              for (const name in LEAGUE_IDS) {
                  const option = document.createElement('option');
                  option.value = LEAGUE_IDS[name];
                  option.textContent = getLeagueDisplayName(LEAGUE_IDS[name]); // Use translated name
                  leagueSelectScorers.appendChild(option);
              }
              leagueSelectScorers.addEventListener('change', (e) => loadTopScorers(e.target.value));
          }
      }

      async function loadLeagueStandings(competitionCode) {
          const standingsContainer = document.getElementById('league-standings-content');
          if (!standingsContainer) return;
          if (!competitionCode) {
              standingsContainer.innerHTML = `<p>${getTranslation('select_league_placeholder_stats')}</p>`;
              return;
          }
          standingsContainer.innerHTML = getTranslation('loading_standings') || 'Loading standings...';

          const apiUrl = `https://api.football-data.org/v4/competitions/${competitionCode}/standings`;
          const data = await fetchData(apiUrl, { headers: { 'X-Auth-Token': API_KEYS.FOOTBALL_DATA } });

          if (data && data.standings && data.standings.length > 0) {
              standingsContainer.innerHTML = '';
              // Assuming the first standing table is the main one (e.g., TOTAL)
              const tableData = data.standings[0].table;
              const table = document.createElement('table');
              table.className = 'standings-table';
              table.innerHTML = `
                  <thead>
                      <tr>
                          <th>#</th>
                          <th>${getTranslation('team_name') || 'Team'}</th>
                          <th>${getTranslation('played') || 'P'}</th>
                          <th>${getTranslation('won') || 'W'}</th>
                          <th>${getTranslation('drawn') || 'D'}</th>
                          <th>${getTranslation('lost') || 'L'}</th>
                          <th>${getTranslation('goals_for') || 'GF'}</th>
                          <th>${getTranslation('goals_against') || 'GA'}</th>
                          <th>${getTranslation('goal_difference') || 'GD'}</th>
                          <th>${getTranslation('points') || 'Pts'}</th>
                      </tr>
                  </thead>
                  <tbody>
                  </tbody>
              `;
              const tbody = table.querySelector('tbody');
              tableData.forEach(team => {
                  const row = tbody.insertRow();
                  row.innerHTML = `
                      <td>${team.position}</td>
                      <td>${team.team.name} ${team.team.crest ? `<img src="${team.team.crest}" alt="${team.team.name}" class="team-crest-small" loading="lazy"/>` : ''}</td>
                      <td>${team.playedGames}</td>
                      <td>${team.won}</td>
                      <td>${team.draw}</td>
                      <td>${team.lost}</td>
                      <td>${team.goalsFor}</td>
                      <td>${team.goalsAgainst}</td>
                      <td>${team.goalDifference}</td>
                      <td>${team.points}</td>
                  `;
              });
              standingsContainer.appendChild(table);
              lazyLoadImages();
          } else {
              standingsContainer.innerHTML = getTranslation('no_standings_found') || 'No standings found for this league or API error.';
          }
      }
      translations.en.loading_standings = "Loading standings..."; translations.fr.loading_standings = "Chargement du classement..."; translations.ar.loading_standings = "جاري تحميل الترتيب...";
      translations.en.team_name = "Team"; translations.fr.team_name = "Équipe"; translations.ar.team_name = "الفريق";
      translations.en.played = "P"; translations.fr.played = "J"; translations.ar.played = "ل";
      translations.en.won = "W"; translations.fr.won = "G"; translations.ar.won = "ف";
      translations.en.drawn = "D"; translations.fr.drawn = "N"; translations.ar.drawn = "ت";
      translations.en.lost = "L"; translations.fr.lost = "P"; translations.ar.lost = "خ";
      translations.en.goals_for = "GF"; translations.fr.goals_for = "BP"; translations.ar.goals_for = "له";
      translations.en.goals_against = "GA"; translations.fr.goals_against = "BC"; translations.ar.goals_against = "عليه";
      translations.en.goal_difference = "GD"; translations.fr.goal_difference = "Diff"; translations.ar.goal_difference = "ف.أ";
      translations.en.points = "Pts"; translations.fr.points = "Pts"; translations.ar.points = "ن";
      translations.en.no_standings_found = "No standings found."; translations.fr.no_standings_found = "Aucun classement trouvé."; translations.ar.no_standings_found = "لم يتم العثور على ترتيب.";


      async function loadTopScorers(competitionCode) {
          const scorersContainer = document.getElementById('top-scorers-content');
          if (!scorersContainer) return;
          if (!competitionCode) {
              scorersContainer.innerHTML = `<p>${getTranslation('select_league_placeholder_scorers')}</p>`;
              return;
          }
          scorersContainer.innerHTML = getTranslation('loading_scorers') || 'Loading top scorers...';

          const apiUrl = `https://api.football-data.org/v4/competitions/${competitionCode}/scorers`;
          const data = await fetchData(apiUrl, { headers: { 'X-Auth-Token': API_KEYS.FOOTBALL_DATA } });

          if (data && data.scorers && data.scorers.length > 0) {
              scorersContainer.innerHTML = '';
              const list = document.createElement('ul');
              list.className = 'scorers-list';
              data.scorers.slice(0, 10).forEach(scorer => { // Display top 10
                  const listItem = document.createElement('li');
                  listItem.innerHTML = `
                      <span class="scorer-name">${scorer.player.name}</span>
                      <span class="scorer-team">(${scorer.team.name})</span> -
                      <span class="scorer-goals">${scorer.goals} ${getTranslation('goals') || 'goals'}</span>
                      ${scorer.assists ? ` (${scorer.assists} ${getTranslation('assists') || 'assists'})` : ''}
                  `;
                  list.appendChild(listItem);
              });
              scorersContainer.appendChild(list);
          } else {
              scorersContainer.innerHTML = getTranslation('no_scorers_found') || 'No top scorers found for this league or API error.';
          }
      }
      translations.en.loading_scorers = "Loading top scorers..."; translations.fr.loading_scorers = "Chargement des meilleurs buteurs..."; translations.ar.loading_scorers = "جاري تحميل الهدافين...";
      translations.en.goals = "goals"; translations.fr.goals = "buts"; translations.ar.goals = "أهداف";
      translations.en.assists = "assists"; translations.fr.assists = "passes déc."; translations.ar.assists = "تمريرات حاسمة";
      translations.en.no_scorers_found = "No top scorers found."; translations.fr.no_scorers_found = "Aucun meilleur buteur trouvé."; translations.ar.no_scorers_found = "لم يتم العثور على هدافين.";


      // --- YouTube API for Videos & Live ---
      // Note: Direct client-side YouTube API calls for search can be complex due to quotas and key exposure.
      // A backend proxy or serverless function is often better for production.
      // For this example, we'll simulate or use simple embeds if possible.
      // The API key provided is a Data API v3 key, good for search.

      async function searchYouTube(query, type = 'video', maxResults = 5) {
          const videosContainer = (type === 'video') ? document.getElementById('videos-content-youtube') : document.getElementById('live-content-youtube');
          if (!videosContainer) return;
          
          videosContainer.innerHTML = getTranslation(`loading_${type}s`) || `Loading ${type}s...`;

          // For "live", we need eventType=live
          const eventTypeParam = (type === 'live') ? '&eventType=live' : '';
          const apiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(query)}&type=video${eventTypeParam}&maxResults=${maxResults}&key=${API_KEYS.YOUTUBE}`;

          try {
              const data = await fetchData(apiUrl);
              if (data && data.items && data.items.length > 0) {
                  videosContainer.innerHTML = '';
                  data.items.forEach(item => {
                      const videoId = item.id.videoId;
                      const videoTitle = item.snippet.title;
                      const videoDiv = document.createElement('div');
                      videoDiv.className = 'youtube-video-item';
                      // Using Plyr for embedding YouTube videos.
                      videoDiv.innerHTML = `
                          <h4>${videoTitle}</h4>
                          <div class="plyr__video-embed js-player">
                              <iframe
                                  src="https://www.youtube.com/embed/${videoId}?origin=https://plyr.io&iv_load_policy=3&modestbranding=1&playsinline=1&showinfo=0&rel=0&enablejsapi=1"
                                  allowfullscreen
                                  allowtransparency
                                  allow="autoplay"
                                  loading="lazy"
                              ></iframe>
                          </div>
                      `;
                      videosContainer.appendChild(videoDiv);
                  });
                  // Initialize Plyr for all new players
                  document.querySelectorAll('.js-player').forEach(playerElement => {
                      // Check if Plyr instance already exists to avoid re-initialization
                      if (!playerElement.plyr) {
                          new Plyr(playerElement);
                      }
                  });
                  // lazyLoadIframes(); // Plyr handles its iframe loading
              } else {
                  videosContainer.innerHTML = getTranslation(`no_${type}s_found`) || `No ${type}s found for "${query}".`;
              }
          } catch (error) {
              console.error(`Error fetching YouTube ${type}s:`, error);
              videosContainer.innerHTML = getTranslation(`error_loading_${type}s`) || `Error loading ${type}s. Check API key or network.`;
          }
      }
      translations.en.loading_videos = "Loading videos..."; translations.fr.loading_videos = "Chargement des vidéos..."; translations.ar.loading_videos = "جاري تحميل الفيديوهات...";
      translations.en.no_videos_found = "No videos found."; translations.fr.no_videos_found = "Aucune vidéo trouvée."; translations.ar.no_videos_found = "لم يتم العثور على فيديوهات.";
      translations.en.error_loading_videos = "Error loading videos."; translations.fr.error_loading_videos = "Erreur de chargement des vidéos."; translations.ar.error_loading_videos = "خطأ في تحميل الفيديوهات.";
      translations.en.loading_lives = "Loading live streams..."; translations.fr.loading_lives = "Chargement des directs..."; translations.ar.loading_lives = "جاري تحميل البث المباشر...";
      translations.en.no_lives_found = "No live streams found."; translations.fr.no_lives_found = "Aucun direct trouvé."; translations.ar.no_lives_found = "لم يتم العثور على بث مباشر.";
      translations.en.error_loading_lives = "Error loading live streams."; translations.fr.error_loading_lives = "Erreur de chargement des directs."; translations.ar.error_loading_lives = "خطأ في تحميل البث المباشر.";

      // function lazyLoadIframes() { // No longer strictly needed as Plyr handles its iframes, but good to keep if other iframes are used
      //   const lazyIframes = document.querySelectorAll('iframe[loading="lazy"]');
      // }


      // --- Contact Form ---
      const contactForm = document.getElementById('contact-form');
      if (contactForm) {
          contactForm.addEventListener('submit', function(event) {
              event.preventDefault();
              const statusDiv = document.getElementById('contact-form-status');
              statusDiv.textContent = getTranslation('sending_message') || 'Sending...';

              // This is a placeholder. For a real contact form on Blogger,
              // you'd typically use a service like Formspree, a Google Form,
              // or a Blogger gadget that handles form submissions.
              // Client-side email sending is not reliable or secure.
              setTimeout(() => {
                  // Simulate success/failure
                  const success = Math.random() > 0.3; // Simulate 70% success
                  if (success) {
                      statusDiv.textContent = getTranslation('message_sent_success') || 'Message sent successfully!';
                      statusDiv.style.color = 'green';
                      contactForm.reset();
                  } else {
                      statusDiv.textContent = getTranslation('message_sent_error') || 'Error sending message. Please try again.';
                      statusDiv.style.color = 'red';
                  }
              }, 1000);
          });
      }
      translations.en.sending_message = "Sending..."; translations.fr.sending_message = "Envoi en cours..."; translations.ar.sending_message = "جارٍ الإرسال...";
      translations.en.message_sent_success = "Message sent successfully!"; translations.fr.message_sent_success = "Message envoyé avec succès !"; translations.ar.message_sent_success = "تم إرسال الرسالة بنجاح!";
      translations.en.message_sent_error = "Error sending message. Please try again."; translations.fr.message_sent_error = "Erreur lors de l'envoi du message. Veuillez réessayer."; translations.ar.message_sent_error = "خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.";


      // --- Video Player Integration ---
      // Plyr for YouTube is initialized dynamically within searchYouTube after elements are added.
      
      // Plyr for static MP4/M3U8 players:
      function initializeStaticPlyrPlayers() {
        const staticPlayers = document.querySelectorAll('.static-plyr-player');
        staticPlayers.forEach(playerElement => {
          if (!playerElement.plyr) { // Avoid re-initialization
            new Plyr(playerElement);
          }
        });
      }

      // Fluid Player for static MP4/M3U8 players:
      function initializeStaticFluidPlayers() {
        const staticFluidPlayers = document.querySelectorAll('.static-fluid-player');
        staticFluidPlayers.forEach(videoElement => {
          if (videoElement && !videoElement.classList.contains('fluid_player_layout_default') && typeof fluidPlayer === 'function') {
            fluidPlayer(videoElement.id, {});
          }
        });
      }

      // Clappr for static MP4/M3U8 players:
      function initializeStaticClapprPlayers() {
        const clapprContainers = document.querySelectorAll('.static-clappr-player');
        clapprContainers.forEach(containerElement => {
          if (containerElement && containerElement.childElementCount === 0 && typeof Clappr === 'object' && typeof HlsJsPlayback === 'function') {
            new Clappr.Player({
              source: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
              parentId: `#${containerElement.id}`,
              plugins: [HlsJsPlayback],
              width: '100%',
              height: '100%',
            });
          }
        });
      }

      // JW Player for static MP4/M3U8 players:
      function initializeStaticJwPlayers() {
        const jwPlayerContainers = document.querySelectorAll('.static-jwplayer');
        jwPlayerContainers.forEach(containerElement => {
          // JW Player initialization is a bit different. It often relies on its library being loaded.
          // Check if the jwplayer function is available and the container hasn't been processed.
          if (typeof jwplayer === 'function' && containerElement.id && !containerElement.classList.contains('jwplayer-setup')) {
            try {
              jwplayer(containerElement.id).setup({
                file: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4", // Example MP4
                // file: "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8", // Example M3U8
                image: "https://via.placeholder.com/640x360.png?text=JW+Player+Poster", // Poster image
                width: "100%",
                aspectratio: "16:9", // Or specific height
                // primary: "html5", // Optional: force html5
                // hlshtml: true, // If using HLS.js with JW Player (often automatic)
              });
              containerElement.classList.add('jwplayer-setup'); // Mark as setup
            } catch (e) {
              console.error("Error setting up JW Player for element ID: " + containerElement.id, e);
              containerElement.textContent = "Error loading JW Player.";
            }
          } else if (typeof jwplayer !== 'function' && containerElement.id && !containerElement.classList.contains('jwplayer-setup')) {
            console.warn("JW Player library not loaded yet for element ID: " + containerElement.id + ". Will retry or check library ID.");
            // Optionally, add a retry mechanism or inform the user to check their JW Player Library ID
          }
        });
      }

      // Call these functions within initializeSite or after relevant content is loaded
      // In initializeSite():
      // ...
      // initializeStaticPlyrPlayers();
      // initializeStaticFluidPlayers();
      // initializeStaticClapprPlayers();
      // initializeStaticJwPlayers();
      // ...
      // Since they are part of the initial HTML structure in the widget, calling in initializeSite is fine.


      // --- Initial calls ---
      initializeSite();
      // Call YouTube search functions with relevant queries
      searchYouTube('faits saillants football récents OR highlights soccer', 'video', 5);
      searchYouTube('match en direct football OR live soccer stream', 'live', 3);

    });
  // ]]>
  </script>
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
<script src="https://cdn.jwplayer.com/libraries/YOUR_JWPLAYER_LIBRARY_ID.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@clappr/player@latest/dist/clappr.min.js"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@clappr/hlsjs-playback@latest/dist/hlsjs-playback.min.js"></script> 
  <script src="https://cdn.fluidplayer.com/v3/current/fluidplayer.min.js"></script>
  <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
</body>
</html>
